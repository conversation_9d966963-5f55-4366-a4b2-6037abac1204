import React, { useState, useContext, useEffect } from 'react';
import { StoreContext } from '../../context/StoreContext';
import './Reviews.css';

const Reviews = () => {
  const { user, token } = useContext(StoreContext);
  const [reviews, setReviews] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  // Mock reviews data for now - in a real app, this would come from an API
  useEffect(() => {
    // Simulate loading reviews
    setTimeout(() => {
      setReviews([
        {
          id: 1,
          restaurantName: "Pizza Palace",
          foodItem: "Margherita Pizza",
          rating: 5,
          comment: "Absolutely delicious! The crust was perfect and the cheese was so fresh.",
          date: "2024-01-15",
          image: null
        },
        {
          id: 2,
          restaurantName: "Burger Barn",
          foodItem: "Classic Cheeseburger",
          rating: 4,
          comment: "Great burger, but could use more seasoning on the fries.",
          date: "2024-01-10",
          image: null
        },
        {
          id: 3,
          restaurantName: "Sushi Spot",
          foodItem: "California Roll",
          rating: 5,
          comment: "Fresh ingredients and excellent presentation. Will order again!",
          date: "2024-01-08",
          image: null
        }
      ]);
      setIsLoading(false);
    }, 1000);
  }, []);

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, index) => (
      <span
        key={index}
        className={`star ${index < rating ? 'filled' : 'empty'}`}
      >
        ★
      </span>
    ));
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (!token) {
    return (
      <div className="reviews-container">
        <div className="reviews-header">
          <h1>My Reviews</h1>
        </div>
        <div className="no-auth-message">
          <p>Please sign in to view your reviews.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="reviews-container">
      <div className="reviews-header">
        <h1>My Reviews</h1>
        <p>Your feedback helps improve our service</p>
      </div>

      {isLoading ? (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading your reviews...</p>
        </div>
      ) : reviews.length === 0 ? (
        <div className="no-reviews">
          <div className="no-reviews-icon">📝</div>
          <h3>No Reviews Yet</h3>
          <p>You haven't written any reviews yet. Order some food and share your experience!</p>
        </div>
      ) : (
        <div className="reviews-list">
          {reviews.map((review) => (
            <div key={review.id} className="review-card">
              <div className="review-header">
                <div className="restaurant-info">
                  <h3>{review.restaurantName}</h3>
                  <p className="food-item">{review.foodItem}</p>
                </div>
                <div className="review-date">
                  {formatDate(review.date)}
                </div>
              </div>
              
              <div className="review-rating">
                {renderStars(review.rating)}
                <span className="rating-text">({review.rating}/5)</span>
              </div>
              
              <div className="review-comment">
                <p>{review.comment}</p>
              </div>
              
              <div className="review-actions">
                <button className="edit-review-btn">Edit Review</button>
                <button className="delete-review-btn">Delete Review</button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default Reviews;
